﻿fold,mse,rmse,mae,mape,accuracy,r2,val_loss,model_path,params
1,3.3937662,1.8422177,0.8433277,6.247756257653236,93.75224374234676,0.9747889917343855,0.03249500288317601,outputs\20250728_220921\best_model_fold1.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
2,3.828119,1.956558,0.452973,4.256102070212364,95.74389792978764,0.9730428736656904,0.015708334045484662,outputs\20250728_220921\best_model_fold2.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
3,3.291775,1.814325,0.38205945,3.210260719060898,96.7897392809391,0.9761942084878683,0.007786061886387567,outputs\20250728_220921\best_model_fold3.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
4,0.085112326,0.29174018,0.22837222,1.3002756051719189,98.69972439482808,0.9993147378554568,0.0025240101385861635,outputs\20250728_220921\best_model_fold4.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
5,1.6337669,1.278189,0.44385344,4.834099858999252,95.16590014100075,0.9844473581761122,0.005084741239746411,outputs\20250728_220921\best_model_fold5.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
6,18.508574,4.3021593,0.7202836,11.46613135933876,88.53386864066124,0.835967481136322,0.03613428585231304,outputs\20250728_220921\best_model_fold6.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
7,4.0243344,2.0060744,0.38986808,4.459962248802185,95.54003775119781,0.9503659456968307,0.015087562147527933,outputs\20250728_220921\best_model_fold7.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
8,80.46414,8.9701805,2.604594,10.514919459819794,89.4850805401802,0.021386384963989258,0.025409677686790626,outputs\20250728_220921\best_model_fold8.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
9,0.605689,0.77826023,0.44047877,3.0532537028193474,96.94674629718065,0.9934550332836807,0.022501943322519462,outputs\20250728_220921\best_model_fold9.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
10,1.0889903,1.043547,0.44766936,3.298202157020569,96.70179784297943,0.9891209043562412,0.0219104983843863,outputs\20250728_220921\best_model_fold10.pth,"{'num_layers': 3, 'hidden_size': 291, 'kernel_size': 12, 'dropout': 0.6, 'learning_rate': 0.0022026595898081655, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 2.0184353301800738e-06}"
