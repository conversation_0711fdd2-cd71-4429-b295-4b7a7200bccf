#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型管理功能的脚本
验证新的模型保存和选择逻辑是否正常工作
"""

import torch
import numpy as np
import pandas as pd
import os
import json
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_model_management():
    """测试模型管理功能"""
    print("=" * 60)
    print("测试模型管理功能")
    print("=" * 60)
    
    # 检查数据文件是否存在
    if not os.path.exists('ev_charging_data.csv'):
        print("❌ 错误：找不到数据文件 ev_charging_data.csv")
        return False
    
    # 检查数据文件大小
    file_size = os.path.getsize('ev_charging_data.csv')
    print(f"✅ 数据文件大小: {file_size / 1024:.2f} KB")
    
    # 读取数据样本
    try:
        df = pd.read_csv('ev_charging_data.csv', encoding='utf-8-sig')
        print(f"✅ 数据加载成功，共 {len(df)} 行，{len(df.columns)} 列")
        print(f"✅ 数据列名: {list(df.columns)}")
        print(f"✅ 时间范围: {df.iloc[0, 0]} 到 {df.iloc[-1, 0]}")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return False
    
    # 检查模型相关模块
    try:
        from train import EVChargingPredictor
        from model import create_model
        from data_preprocessing import DataPreprocessor
        print("✅ 所有模块导入成功")
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    
    # 测试模型管理功能
    try:
        # 创建预测器实例
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"✅ 使用设备: {device}")
        
        predictor = EVChargingPredictor(device=device)
        print("✅ 预测器创建成功")
        
        # 测试新增的方法
        print("\n测试新增的模型管理方法:")
        
        # 模拟fold_models数据
        fold_models = [
            {'fold': 1, 'model_path': 'test1.pth', 'val_loss': 0.5, 'mse': 0.3, 'mape': 5.0, 'r2': 0.95},
            {'fold': 2, 'model_path': 'test2.pth', 'val_loss': 0.3, 'mse': 0.2, 'mape': 3.0, 'r2': 0.97},
            {'fold': 3, 'model_path': 'test3.pth', 'val_loss': 0.7, 'mse': 0.4, 'mape': 7.0, 'r2': 0.93}
        ]
        
        # 测试选择最佳模型
        best_model = predictor.select_best_global_model(fold_models)
        print(f"✅ 最佳模型选择成功: 第{best_model['fold']}折")
        
        # 测试配置
        config = {
            'input_size': 10,
            'hidden_size': 64,
            'num_layers': 2,
            'output_size': 1,
            'kernel_size': 3,
            'dropout': 0.3,
            'learning_rate': 0.001,
            'loss_type': 'MSELoss',
            'optimizer_type': 'Adam',
            'weight_decay': 1e-5
        }
        
        print("✅ 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_previous_results():
    """分析上次运行的结果"""
    print("\n" + "=" * 60)
    print("分析上次运行结果")
    print("=" * 60)
    
    results_dir = "outputs/20250718_165535"
    if not os.path.exists(results_dir):
        print("❌ 找不到上次运行的结果目录")
        return
    
    # 统计模型文件数量
    model_files = [f for f in os.listdir(results_dir) if f.endswith('.pth')]
    print(f"📊 上次运行产生的模型文件数量: {len(model_files)}")
    
    # 按类型分类
    fold_models = [f for f in model_files if 'fold' in f]
    final_models = [f for f in model_files if 'fold' not in f]
    
    print(f"📊 交叉验证模型文件: {len(fold_models)}")
    print(f"📊 最终模型文件: {len(final_models)}")
    
    # 计算文件大小
    total_size = 0
    for f in model_files:
        file_path = os.path.join(results_dir, f)
        total_size += os.path.getsize(file_path)
    
    print(f"📊 模型文件总大小: {total_size / 1024 / 1024:.2f} MB")
    
    # 读取交叉验证结果
    cv_file = os.path.join(results_dir, 'cross_validation_results_20250718_170647.csv')
    if os.path.exists(cv_file):
        cv_df = pd.read_csv(cv_file)
        print(f"📊 交叉验证结果:")
        print(f"   - 平均MAPE: {cv_df['mape'].mean():.2f}%")
        print(f"   - 平均R2: {cv_df['r2'].mean():.4f}")
        print(f"   - 最佳折: 第{cv_df.loc[cv_df['mape'].idxmin(), 'fold']}折 (MAPE: {cv_df['mape'].min():.2f}%)")

if __name__ == "__main__":
    # 运行测试
    success = test_model_management()
    
    # 分析上次结果
    analyze_previous_results()
    
    if success:
        print("\n🎉 所有测试通过！新的模型管理功能已就绪。")
        print("\n📝 主要改进:")
        print("   ✅ 每折只保留一个最佳模型文件")
        print("   ✅ 自动选择全局最佳模型")
        print("   ✅ 自动清理冗余模型文件")
        print("   ✅ 统一的最终部署模型")
        print("   ✅ 完整的模型配置保存")
    else:
        print("\n❌ 测试失败，请检查代码修改。")
