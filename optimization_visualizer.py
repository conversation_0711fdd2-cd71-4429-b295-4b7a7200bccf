import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import pandas as pd
import json
import os
from typing import Dict, List, Optional
import pickle
from datetime import datetime

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizationVisualizer:
    """
    贝叶斯优化结果可视化器
    
    提供全面的优化过程分析和可视化功能
    """
    
    def __init__(self, output_dir: str):
        """
        初始化可视化器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.history_data = None
        self.best_params = None
        
        # 加载优化历史数据
        self._load_optimization_data()
    
    def _load_optimization_data(self):
        """加载优化历史数据"""
        try:
            # 加载优化历史
            history_file = os.path.join(self.output_dir, 'optimization_history.pkl')
            if os.path.exists(history_file):
                with open(history_file, 'rb') as f:
                    self.history_data = pickle.load(f)
            
            # 加载最优参数
            params_file = os.path.join(self.output_dir, 'best_params_advanced.json')
            if os.path.exists(params_file):
                with open(params_file, 'r', encoding='utf-8') as f:
                    self.best_params = json.load(f)
            
            print(f"成功加载优化数据，历史记录: {len(self.history_data.get('history', []))} 条")
            
        except Exception as e:
            print(f"加载优化数据失败: {e}")
    
    def create_comprehensive_report(self):
        """创建全面的优化报告"""
        if not self.history_data:
            print("没有可用的优化历史数据")
            return
        
        # 创建多页面报告
        self._create_optimization_overview()
        self._create_parameter_analysis()
        self._create_convergence_analysis()
        self._create_performance_comparison()
        self._create_sensitivity_analysis()
        
        print(f"全面优化报告已生成，保存在: {self.output_dir}")
    
    def _create_optimization_overview(self):
        """创建优化概览图"""
        history = self.history_data.get('history', [])
        if not history:
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 评分历史和最佳评分轨迹
        scores = [h['score'] for h in history]
        final_scores = [h['final_score'] for h in history]
        iterations = range(1, len(scores) + 1)
        
        ax1.plot(iterations, scores, 'b-', alpha=0.6, label='验证损失', linewidth=1)
        ax1.plot(iterations, final_scores, 'r-', alpha=0.8, label='综合评分', linewidth=1.5)
        ax1.plot(iterations, np.minimum.accumulate(final_scores), 'g-', linewidth=2, label='最佳综合评分')
        ax1.set_title('优化过程 - 评分变化轨迹', fontsize=14, fontweight='bold')
        ax1.set_xlabel('迭代次数')
        ax1.set_ylabel('评分')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 多目标优化组件分析
        complexities = [h['complexity'] for h in history]
        stabilities = [h['stability'] for h in history]
        uncertainties = [h['uncertainty'] for h in history]
        
        ax2.plot(iterations, complexities, 'orange', alpha=0.7, label='模型复杂度')
        ax2.plot(iterations, stabilities, 'purple', alpha=0.7, label='训练稳定性')
        ax2.plot(iterations, uncertainties, 'cyan', alpha=0.7, label='预测不确定性')
        ax2.set_title('多目标优化组件分析', fontsize=14, fontweight='bold')
        ax2.set_xlabel('迭代次数')
        ax2.set_ylabel('归一化值')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 评分分布直方图
        ax3.hist(final_scores, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        ax3.axvline(min(final_scores), color='red', linestyle='--', linewidth=2, label=f'最佳: {min(final_scores):.4f}')
        ax3.axvline(np.mean(final_scores), color='orange', linestyle='--', linewidth=2, label=f'平均: {np.mean(final_scores):.4f}')
        ax3.set_title('评分分布直方图', fontsize=14, fontweight='bold')
        ax3.set_xlabel('综合评分')
        ax3.set_ylabel('频次')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 4. 优化效率分析
        improvement_rates = []
        window_size = 10
        for i in range(window_size, len(final_scores)):
            recent_best = min(final_scores[i-window_size:i])
            current_best = min(final_scores[:i+1])
            improvement = (recent_best - current_best) / recent_best if recent_best != 0 else 0
            improvement_rates.append(improvement * 100)
        
        if improvement_rates:
            ax4.plot(range(window_size+1, len(final_scores)+1), improvement_rates, 'green', linewidth=2)
            ax4.set_title('优化效率分析（滑动窗口改进率）', fontsize=14, fontweight='bold')
            ax4.set_xlabel('迭代次数')
            ax4.set_ylabel('改进率 (%)')
            ax4.grid(True, alpha=0.3)
        else:
            ax4.text(0.5, 0.5, '数据不足以计算改进率', ha='center', va='center', transform=ax4.transAxes)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'optimization_overview.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_parameter_analysis(self):
        """创建参数分析图"""
        history = self.history_data.get('history', [])
        if not history:
            return
        
        # 提取参数数据
        param_data = {}
        scores = []
        
        for h in history:
            scores.append(h['final_score'])
            for param_name, param_value in h['params'].items():
                if param_name not in param_data:
                    param_data[param_name] = []
                param_data[param_name].append(param_value)
        
        # 计算数值参数的相关性
        numeric_params = {}
        for param_name, values in param_data.items():
            if all(isinstance(v, (int, float)) for v in values):
                numeric_params[param_name] = values
        
        if len(numeric_params) < 2:
            return
        
        # 创建参数相关性热力图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. 参数与评分的相关性
        correlations = {}
        for param_name, values in numeric_params.items():
            corr = np.corrcoef(values, scores)[0, 1]
            correlations[param_name] = abs(corr) if not np.isnan(corr) else 0
        
        sorted_corr = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
        param_names, corr_values = zip(*sorted_corr)
        
        bars = ax1.barh(param_names, corr_values, color='lightcoral')
        ax1.set_title('参数重要性排序（与评分相关性）', fontsize=14, fontweight='bold')
        ax1.set_xlabel('相关系数绝对值')
        
        # 添加数值标签
        for i, (name, value) in enumerate(sorted_corr):
            ax1.text(value + 0.01, i, f'{value:.3f}', va='center')
        
        # 2. 参数间相关性热力图
        param_df = pd.DataFrame(numeric_params)
        corr_matrix = param_df.corr()
        
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                   square=True, ax=ax2, cbar_kws={'shrink': 0.8})
        ax2.set_title('参数间相关性热力图', fontsize=14, fontweight='bold')
        
        # 3. 最重要参数的分布
        if sorted_corr:
            most_important_param = sorted_corr[0][0]
            values = numeric_params[most_important_param]
            
            ax3.scatter(values, scores, alpha=0.6, color='blue')
            ax3.set_title(f'最重要参数分布: {most_important_param}', fontsize=14, fontweight='bold')
            ax3.set_xlabel(most_important_param)
            ax3.set_ylabel('综合评分')
            ax3.grid(True, alpha=0.3)
            
            # 添加趋势线
            z = np.polyfit(values, scores, 1)
            p = np.poly1d(z)
            ax3.plot(values, p(values), "r--", alpha=0.8, linewidth=2)
        
        # 4. 参数优化轨迹（选择几个重要参数）
        top_params = [name for name, _ in sorted_corr[:3]]
        colors = ['red', 'blue', 'green']
        
        for i, param_name in enumerate(top_params):
            values = numeric_params[param_name]
            # 归一化到0-1范围以便比较
            normalized_values = (np.array(values) - min(values)) / (max(values) - min(values)) if max(values) != min(values) else np.zeros_like(values)
            ax4.plot(range(len(normalized_values)), normalized_values, 
                    color=colors[i], alpha=0.7, label=param_name, linewidth=2)
        
        ax4.set_title('重要参数优化轨迹（归一化）', fontsize=14, fontweight='bold')
        ax4.set_xlabel('迭代次数')
        ax4.set_ylabel('归一化参数值')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'parameter_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_convergence_analysis(self):
        """创建收敛性分析图"""
        convergence_data = self.history_data.get('convergence', [])
        if not convergence_data:
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        iterations = [cd['iteration'] for cd in convergence_data]
        best_scores = [cd['best_score'] for cd in convergence_data]
        current_scores = [cd['current_score'] for cd in convergence_data]
        
        # 1. 收敛轨迹
        ax1.plot(iterations, current_scores, 'b-', alpha=0.5, label='当前评分', linewidth=1)
        ax1.plot(iterations, best_scores, 'r-', linewidth=2, label='历史最佳评分')
        ax1.fill_between(iterations, current_scores, best_scores, alpha=0.2, color='gray')
        ax1.set_title('贝叶斯优化收敛轨迹', fontsize=14, fontweight='bold')
        ax1.set_xlabel('迭代次数')
        ax1.set_ylabel('评分')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 收敛速度分析
        improvements = []
        for i in range(1, len(best_scores)):
            improvement = best_scores[i-1] - best_scores[i]
            improvements.append(improvement)
        
        if improvements:
            ax2.plot(iterations[1:], improvements, 'g-', linewidth=2, marker='o', markersize=4)
            ax2.set_title('每次迭代的改进量', fontsize=14, fontweight='bold')
            ax2.set_xlabel('迭代次数')
            ax2.set_ylabel('评分改进量')
            ax2.grid(True, alpha=0.3)
            
            # 添加零线
            ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        # 3. 收敛稳定性分析
        if len(best_scores) > 20:
            # 计算滑动窗口内的改进
            window_size = 10
            stability_scores = []
            for i in range(window_size, len(best_scores)):
                recent_improvement = best_scores[i-window_size] - best_scores[i]
                stability_scores.append(recent_improvement)
            
            ax3.plot(iterations[window_size:], stability_scores, 'purple', linewidth=2)
            ax3.set_title(f'收敛稳定性（{window_size}步滑动窗口）', fontsize=14, fontweight='bold')
            ax3.set_xlabel('迭代次数')
            ax3.set_ylabel('窗口内改进量')
            ax3.grid(True, alpha=0.3)
        else:
            ax3.text(0.5, 0.5, '数据不足以分析收敛稳定性', ha='center', va='center', transform=ax3.transAxes)
        
        # 4. 收敛预测
        if len(best_scores) > 10:
            # 使用指数拟合预测收敛
            x_data = np.array(iterations[-20:])  # 使用最后20个点
            y_data = np.array(best_scores[-20:])
            
            try:
                # 指数衰减拟合
                def exp_decay(x, a, b, c):
                    return a * np.exp(-b * x) + c
                
                from scipy.optimize import curve_fit
                popt, _ = curve_fit(exp_decay, x_data - x_data[0], y_data, maxfev=1000)
                
                # 预测未来收敛
                future_x = np.linspace(iterations[-1], iterations[-1] + 50, 50)
                future_y = exp_decay(future_x - x_data[0], *popt)
                
                ax4.plot(iterations, best_scores, 'b-', linewidth=2, label='实际收敛')
                ax4.plot(future_x, future_y, 'r--', linewidth=2, label='预测收敛')
                ax4.set_title('收敛预测分析', fontsize=14, fontweight='bold')
                ax4.set_xlabel('迭代次数')
                ax4.set_ylabel('预测评分')
                ax4.legend()
                ax4.grid(True, alpha=0.3)
                
            except:
                ax4.text(0.5, 0.5, '收敛预测拟合失败', ha='center', va='center', transform=ax4.transAxes)
        else:
            ax4.text(0.5, 0.5, '数据不足以进行收敛预测', ha='center', va='center', transform=ax4.transAxes)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'convergence_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_performance_comparison(self):
        """创建性能对比分析"""
        history = self.history_data.get('history', [])
        if not history:
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # 提取性能指标
        scores = [h['score'] for h in history]
        complexities = [h['complexity'] for h in history]
        stabilities = [h['stability'] for h in history]
        uncertainties = [h['uncertainty'] for h in history]
        
        # 1. 性能-复杂度权衡分析
        scatter = ax1.scatter(complexities, scores, c=stabilities, cmap='viridis', alpha=0.7, s=50)
        ax1.set_title('性能-复杂度权衡分析', fontsize=14, fontweight='bold')
        ax1.set_xlabel('模型复杂度')
        ax1.set_ylabel('验证损失')
        cbar1 = plt.colorbar(scatter, ax=ax1)
        cbar1.set_label('训练稳定性')
        ax1.grid(True, alpha=0.3)
        
        # 2. 帕累托前沿分析
        # 找到帕累托最优解（低损失 + 低复杂度）
        pareto_indices = []
        for i, (score, complexity) in enumerate(zip(scores, complexities)):
            is_pareto = True
            for j, (other_score, other_complexity) in enumerate(zip(scores, complexities)):
                if i != j and other_score <= score and other_complexity <= complexity and (other_score < score or other_complexity < complexity):
                    is_pareto = False
                    break
            if is_pareto:
                pareto_indices.append(i)
        
        ax2.scatter(complexities, scores, alpha=0.5, color='lightblue', label='所有解')
        if pareto_indices:
            pareto_complexities = [complexities[i] for i in pareto_indices]
            pareto_scores = [scores[i] for i in pareto_indices]
            ax2.scatter(pareto_complexities, pareto_scores, color='red', s=100, label='帕累托前沿', marker='*')
        ax2.set_title('帕累托前沿分析', fontsize=14, fontweight='bold')
        ax2.set_xlabel('模型复杂度')
        ax2.set_ylabel('验证损失')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 不确定性分析
        ax3.scatter(scores, uncertainties, alpha=0.7, color='orange')
        ax3.set_title('预测不确定性分析', fontsize=14, fontweight='bold')
        ax3.set_xlabel('验证损失')
        ax3.set_ylabel('预测不确定性')
        ax3.grid(True, alpha=0.3)
        
        # 添加趋势线
        if len(scores) > 5:
            z = np.polyfit(scores, uncertainties, 1)
            p = np.poly1d(z)
            ax3.plot(scores, p(scores), "r--", alpha=0.8, linewidth=2)
        
        # 4. 综合性能雷达图
        if self.best_params:
            # 选择最佳配置和几个对比配置
            best_idx = np.argmin([h['final_score'] for h in history])
            worst_idx = np.argmax([h['final_score'] for h in history])
            median_idx = np.argsort([h['final_score'] for h in history])[len(history)//2]
            
            configs = {
                '最佳配置': best_idx,
                '中等配置': median_idx,
                '最差配置': worst_idx
            }
            
            # 准备雷达图数据
            categories = ['验证损失\n(反向)', '模型复杂度\n(反向)', '训练稳定性', '预测确定性\n(反向)']
            
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            angles += angles[:1]  # 闭合图形
            
            ax4 = plt.subplot(2, 2, 4, projection='polar')
            
            colors = ['red', 'blue', 'green']
            for i, (config_name, idx) in enumerate(configs.items()):
                values = [
                    1 - (scores[idx] - min(scores)) / (max(scores) - min(scores)) if max(scores) != min(scores) else 0.5,  # 反向验证损失
                    1 - complexities[idx],  # 反向复杂度
                    stabilities[idx],  # 稳定性
                    1 - uncertainties[idx] if max(uncertainties) > 0 else 0.5  # 反向不确定性
                ]
                values += values[:1]  # 闭合图形
                
                ax4.plot(angles, values, 'o-', linewidth=2, label=config_name, color=colors[i])
                ax4.fill(angles, values, alpha=0.25, color=colors[i])
            
            ax4.set_xticks(angles[:-1])
            ax4.set_xticklabels(categories)
            ax4.set_ylim(0, 1)
            ax4.set_title('配置性能对比雷达图', fontsize=14, fontweight='bold', pad=20)
            ax4.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _create_sensitivity_analysis(self):
        """创建敏感性分析图"""
        history = self.history_data.get('history', [])
        if len(history) < 20:
            return
        
        # 提取数值参数
        param_data = {}
        scores = []
        
        for h in history:
            scores.append(h['final_score'])
            for param_name, param_value in h['params'].items():
                if isinstance(param_value, (int, float)):
                    if param_name not in param_data:
                        param_data[param_name] = []
                    param_data[param_name].append(param_value)
        
        if len(param_data) < 4:
            return
        
        # 选择最重要的4个参数进行敏感性分析
        correlations = {}
        for param_name, values in param_data.items():
            corr = abs(np.corrcoef(values, scores)[0, 1])
            correlations[param_name] = corr if not np.isnan(corr) else 0
        
        top_params = sorted(correlations.items(), key=lambda x: x[1], reverse=True)[:4]
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()
        
        for i, (param_name, _) in enumerate(top_params):
            values = param_data[param_name]
            
            # 创建参数值的分箱
            n_bins = min(10, len(set(values)))
            if n_bins < 3:
                continue
                
            # 计算每个分箱的统计信息
            bins = np.linspace(min(values), max(values), n_bins + 1)
            bin_centers = (bins[:-1] + bins[1:]) / 2
            bin_means = []
            bin_stds = []
            
            for j in range(len(bins) - 1):
                mask = (np.array(values) >= bins[j]) & (np.array(values) < bins[j + 1])
                if j == len(bins) - 2:  # 最后一个分箱包含右边界
                    mask = (np.array(values) >= bins[j]) & (np.array(values) <= bins[j + 1])
                
                bin_scores = [scores[k] for k in range(len(scores)) if mask[k]]
                if bin_scores:
                    bin_means.append(np.mean(bin_scores))
                    bin_stds.append(np.std(bin_scores))
                else:
                    bin_means.append(np.nan)
                    bin_stds.append(np.nan)
            
            # 过滤掉NaN值
            valid_indices = ~np.isnan(bin_means)
            valid_centers = bin_centers[valid_indices]
            valid_means = np.array(bin_means)[valid_indices]
            valid_stds = np.array(bin_stds)[valid_indices]
            
            if len(valid_centers) > 0:
                axes[i].errorbar(valid_centers, valid_means, yerr=valid_stds, 
                               fmt='o-', capsize=5, capthick=2, linewidth=2, markersize=6)
                axes[i].set_title(f'{param_name} 敏感性分析', fontsize=12, fontweight='bold')
                axes[i].set_xlabel(param_name)
                axes[i].set_ylabel('平均评分 ± 标准差')
                axes[i].grid(True, alpha=0.3)
                
                # 添加最优区域标注
                best_idx = np.argmin(valid_means)
                axes[i].axvline(valid_centers[best_idx], color='red', linestyle='--', 
                              alpha=0.7, label=f'最优值: {valid_centers[best_idx]:.3f}')
                axes[i].legend()
        
        plt.suptitle('参数敏感性分析', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'sensitivity_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_summary_report(self):
        """生成优化总结报告"""
        if not self.history_data or not self.best_params:
            return
        
        history = self.history_data.get('history', [])
        
        report_path = os.path.join(self.output_dir, 'optimization_summary_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("增强贝叶斯优化总结报告\n")
            f.write("=" * 80 + "\n\n")
            
            f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"优化历史记录数: {len(history)}\n\n")
            
            if history:
                scores = [h['final_score'] for h in history]
                f.write("=== 优化性能统计 ===\n")
                f.write(f"最佳评分: {min(scores):.6f}\n")
                f.write(f"平均评分: {np.mean(scores):.6f}\n")
                f.write(f"评分标准差: {np.std(scores):.6f}\n")
                f.write(f"评分改进: {max(scores) - min(scores):.6f}\n\n")
                
                f.write("=== 最优参数配置 ===\n")
                for key, value in self.best_params.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # 计算参数重要性
                param_data = {}
                for h in history:
                    for param_name, param_value in h['params'].items():
                        if isinstance(param_value, (int, float)):
                            if param_name not in param_data:
                                param_data[param_name] = []
                            param_data[param_name].append(param_value)
                
                f.write("=== 参数重要性排序 ===\n")
                correlations = {}
                for param_name, values in param_data.items():
                    corr = abs(np.corrcoef(values, scores)[0, 1])
                    correlations[param_name] = corr if not np.isnan(corr) else 0
                
                sorted_importance = sorted(correlations.items(), key=lambda x: x[1], reverse=True)
                for i, (param_name, importance) in enumerate(sorted_importance, 1):
                    f.write(f"{i}. {param_name}: {importance:.4f}\n")
                
                f.write("\n=== 优化建议 ===\n")
                f.write("1. 重点关注高重要性参数的进一步调优\n")
                f.write("2. 考虑增加优化迭代次数以获得更好的结果\n")
                f.write("3. 可以尝试不同的获取函数以改善探索-利用平衡\n")
                f.write("4. 建议在更大的数据集上验证最优配置的泛化性能\n")
        
        print(f"优化总结报告已生成: {report_path}")

def create_optimization_report(output_dir: str):
    """创建完整的优化报告"""
    visualizer = OptimizationVisualizer(output_dir)
    visualizer.create_comprehensive_report()
    visualizer.generate_summary_report()
    print("完整的优化报告已生成完成！")
