﻿fold,mse,rmse,mae,mape,accuracy,r2,params
1,269.04208,16.402502,8.217736,22.839166224002838,77.16083377599716,0.6580987870693207,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
2,260.05176,16.12612,2.9333675,8.499778062105179,91.50022193789482,0.6709052324295044,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
3,200.95158,14.175739,7.5896535,25.15473961830139,74.84526038169861,0.7588901370763779,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
4,157.80515,12.562052,3.161647,7.141859084367752,92.85814091563225,0.8375602960586548,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
5,96.37315,9.816983,1.9467742,3.6458760499954224,96.35412395000458,0.8804503157734871,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
6,133.3502,11.547736,2.4379675,5.488386377692223,94.51161362230778,0.8575083762407303,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
7,86.57655,9.304652,4.353696,15.321432054042816,84.67856794595718,0.8865042105317116,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
8,100.632034,10.031552,1.6669191,2.5908641517162323,97.40913584828377,0.8882431462407112,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
9,166.84679,12.916919,4.7617154,15.231768786907196,84.7682312130928,0.7982892990112305,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
10,61.953762,7.8710713,1.5307608,3.681408241391182,96.31859175860882,0.935778483748436,"{'num_layers': 2, 'hidden_size': 64, 'kernel_size': 12, 'dropout': 0.13847427651814637, 'learning_rate': 0.002937485092537012, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 5.95012633411071e-05}"
