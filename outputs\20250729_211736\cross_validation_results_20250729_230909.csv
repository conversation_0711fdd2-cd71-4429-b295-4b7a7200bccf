﻿fold,mse,rmse,mae,mape,accuracy,r2,val_loss,model_path,params
1,5.1931634,2.2788513,0.9756692,8.520708978176117,91.47929102182388,0.9614219479262829,0.028534908158083756,outputs\20250729_211736\best_model_fold1.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
2,5.9024296,2.4294915,0.5063836,4.3199170380830765,95.68008296191692,0.958435844630003,0.015954417098934453,outputs\20250729_211736\best_model_fold2.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
3,1.6482302,1.2838342,0.8847834,6.793465465307236,93.20653453469276,0.9880801625549793,0.015594047649453083,outputs\20250729_211736\best_model_fold3.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
4,0.2446598,0.49463096,0.32269958,2.3023750633001328,97.69762493669987,0.9980301784817129,0.0032549648894928396,outputs\20250729_211736\best_model_fold4.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
5,4.3013964,2.0739808,0.63580346,8.45702737569809,91.54297262430191,0.9590528644621372,0.0054424552169318,outputs\20250729_211736\best_model_fold5.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
6,18.66806,4.320655,0.85463387,10.991369932889938,89.00863006711006,0.8345540314912796,0.0357515438615034,outputs\20250729_211736\best_model_fold6.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
7,0.62797105,0.79244626,0.35385886,5.669731646776199,94.3302683532238,0.9922549310140312,0.010692821194728216,outputs\20250729_211736\best_model_fold7.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
8,16.29844,4.0371327,0.6694139,8.765023946762085,91.23497605323792,0.8017765879631042,0.024688332768467564,outputs\20250729_211736\best_model_fold8.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
9,1.1908976,1.0912825,0.48423657,3.903483599424362,96.09651640057564,0.9871313739567995,0.024997885183741648,outputs\20250729_211736\best_model_fold9.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
10,3.1949534,1.7874433,0.4440374,3.8964465260505676,96.10355347394943,0.9680821746587753,0.027710421631733578,outputs\20250729_211736\best_model_fold10.pth,"{'num_layers': 1, 'hidden_size': 64, 'kernel_size': 2, 'gru_layers': 1, 'bidirectional_gru': False, 'dropout': 0.1, 'learning_rate': 0.00803645584541949, 'weight_decay': 1e-06, 'batch_size': 128, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'sequence_length': 42, 'k_best_features': 5, 'data_augmentation_strength': 1.4763121664419259, 'early_stopping_patience': 5, 'lr_scheduler_factor': 0.43109381919667056, 'lr_scheduler_patience': 14, 'l2_regularization': 0.14490674907142564}"
