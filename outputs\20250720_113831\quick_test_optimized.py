#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试优化后的模型管理功能
使用小规模参数进行快速验证
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import pandas as pd
import os
import json
import logging
from datetime import datetime
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler

# 导入项目模块
from train import EVChargingPredictor
from model import create_model
from data_preprocessing import DataPreprocessor

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def quick_test_training():
    """快速测试训练流程"""
    print("=" * 60)
    print("快速测试优化后的训练流程")
    print("=" * 60)
    
    try:
        # 设置随机种子
        torch.manual_seed(42)
        np.random.seed(42)
        
        # 检测设备
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"使用设备: {device}")
        
        # 创建预测器
        predictor = EVChargingPredictor(device=device)
        print("✅ 预测器创建成功")
        
        # 准备数据（小规模测试）
        print("准备数据...")
        train_loader, val_loader, test_loader, target_scaler = predictor.prepare_data('ev_charging_data.csv')
        print(f"✅ 数据准备完成，输入特征维度: {predictor.input_size}")
        
        # 使用预设的最优参数（避免耗时的超参数优化）
        best_params = {
            'num_layers': 1,
            'hidden_size': 66,
            'kernel_size': 12,
            'dropout': 0.18461062540589218,
            'learning_rate': 0.01,
            'loss_type': 'SmoothL1Loss',
            'optimizer_type': 'Adam',
            'weight_decay': 3.339828230348852e-05
        }
        print("✅ 使用预设最优参数")
        
        # 快速交叉验证（只用3折，减少训练时间）
        print("开始快速交叉验证（3折）...")
        
        # 临时修改交叉验证参数
        original_patience = 15
        original_epochs = 400
        
        # 创建小规模测试配置
        config = {
            'input_size': predictor.input_size,
            'hidden_size': best_params['hidden_size'],
            'num_layers': best_params['num_layers'],
            'output_size': 1,
            'kernel_size': best_params['kernel_size'],
            'dropout': best_params['dropout'],
            'learning_rate': best_params['learning_rate'],
            'loss_type': best_params['loss_type'],
            'optimizer_type': best_params['optimizer_type'],
            'weight_decay': best_params['weight_decay']
        }
        
        # 模拟快速交叉验证
        print("模拟交叉验证过程...")
        
        # 模拟fold_models数据（基于真实的交叉验证结果）
        fold_models = [
            {'fold': 1, 'model_path': os.path.join(predictor.output_dir, 'best_model_fold1.pth'), 
             'val_loss': 2.8481295, 'mse': 2.8481295, 'mape': 4.20, 'r2': 0.9788},
            {'fold': 2, 'model_path': os.path.join(predictor.output_dir, 'best_model_fold2.pth'), 
             'val_loss': 4.8981466, 'mse': 4.8981466, 'mape': 5.52, 'r2': 0.9655},
            {'fold': 3, 'model_path': os.path.join(predictor.output_dir, 'best_model_fold3.pth'), 
             'val_loss': 0.4098385, 'mse': 0.4098385, 'mape': 1.65, 'r2': 0.9970}
        ]
        
        # 创建模拟的模型文件
        model = create_model(config).to(predictor.device)
        for fold_info in fold_models:
            torch.save(model.state_dict(), fold_info['model_path'])
            print(f"✅ 创建模拟模型文件: {os.path.basename(fold_info['model_path'])}")
        
        # 测试全局最佳模型选择
        print("\n测试全局最佳模型选择...")
        best_model_info = predictor.select_best_global_model(fold_models)
        print(f"✅ 选择了第{best_model_info['fold']}折作为最佳模型")
        
        # 测试模型清理
        print("\n测试模型清理...")
        predictor.cleanup_redundant_models(fold_models, best_model_info['model_path'])
        
        # 验证清理结果
        remaining_files = [f for f in os.listdir(predictor.output_dir) if f.endswith('.pth')]
        print(f"✅ 清理后剩余模型文件: {len(remaining_files)} 个")
        for f in remaining_files:
            print(f"   - {f}")
        
        # 测试最终模型保存
        print("\n测试最终模型保存...")
        final_model_path = predictor.save_final_model(best_model_info['model_path'], config)
        print(f"✅ 最终模型保存成功: {os.path.basename(final_model_path)}")
        
        # 验证最终文件
        if os.path.exists(final_model_path):
            print(f"✅ 最终模型文件存在，大小: {os.path.getsize(final_model_path) / 1024:.2f} KB")
        
        config_path = os.path.join(predictor.output_dir, 'final_model_config.json')
        if os.path.exists(config_path):
            print(f"✅ 配置文件存在，大小: {os.path.getsize(config_path)} bytes")
            
            # 验证配置文件内容
            with open(config_path, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
            print(f"✅ 配置文件内容验证通过，模型类型: {saved_config['model_type']}")
        
        # 测试模型加载
        print("\n测试模型加载...")
        test_model = create_model(config).to(predictor.device)
        test_model.load_state_dict(torch.load(final_model_path))
        test_model.eval()
        print("✅ 最终模型加载成功")
        
        # 简单预测测试
        with torch.no_grad():
            for X_batch, y_batch in test_loader:
                X_batch = X_batch.to(predictor.device)
                predictions = test_model(X_batch)
                print(f"✅ 预测测试成功，输出形状: {predictions.shape}")
                break
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！优化后的模型管理功能正常工作")
        print("=" * 60)
        
        # 输出测试总结
        print("\n📊 测试总结:")
        print(f"   - 输出目录: {predictor.output_dir}")
        print(f"   - 最佳模型: 第{best_model_info['fold']}折")
        print(f"   - 最佳验证损失: {best_model_info['val_loss']:.6f}")
        print(f"   - 最佳MAPE: {best_model_info['mape']:.2f}%")
        print(f"   - 最佳R2: {best_model_info['r2']:.4f}")
        print(f"   - 最终模型文件: {os.path.basename(final_model_path)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = quick_test_training()
    
    if success:
        print("\n✅ 快速测试完成，模型管理优化成功！")
        print("\n📝 下一步可以:")
        print("   1. 运行完整的训练: python train.py")
        print("   2. 使用最终模型进行预测")
        print("   3. 部署模型到生产环境")
    else:
        print("\n❌ 测试失败，请检查代码修改。")
