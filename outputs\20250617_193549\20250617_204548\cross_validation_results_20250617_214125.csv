﻿fold,mse,rmse,mae,mape,accuracy,r2,params
1,150.7635,12.278579,2.9031327,8.62099602818489,91.37900397181511,0.808408334851265,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
2,206.85313,14.38239,3.9142492,11.54380515217781,88.45619484782219,0.7382279336452484,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
3,15.0173645,3.8752244,1.5723858,5.4809752851724625,94.51902471482754,0.9819815568625927,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
4,110.74803,10.523689,1.7744838,3.7035733461380005,96.296426653862,0.8859994262456894,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
5,70.23142,8.380419,1.127494,1.8491355702280998,98.1508644297719,0.9128788039088249,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
6,161.99554,12.727747,2.2876968,5.066587030887604,94.9334129691124,0.8268993347883224,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
7,74.423775,8.626922,2.680001,7.823988050222397,92.1760119497776,0.9024356603622437,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
8,67.77115,8.232324,1.4333105,3.4108754247426987,96.5891245752573,0.9247367829084396,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
9,118.9869,10.908112,1.5289772,3.339880332350731,96.66011966764927,0.8561498671770096,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
10,72.3929,8.508402,1.8436048,4.132185131311417,95.86781486868858,0.9249572232365608,"{'num_layers': 7, 'hidden_size': 512, 'kernel_size': 2, 'dropout': 0.153763154942942, 'learning_rate': 0.0017290897935648712, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
