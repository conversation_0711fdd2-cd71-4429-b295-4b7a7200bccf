﻿fold,mse,rmse,mae,mape,accuracy,r2,params
1,2.8481295,1.6876402,0.58340406,4.200845956802368,95.79915404319763,0.9788423199206591,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
2,4.8981466,2.2131758,0.6650927,5.521537736058235,94.47846226394176,0.9655078761279583,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
3,0.4098385,0.6401863,0.22787838,1.6493605449795723,98.35063945502043,0.9970360884908587,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
4,0.15229128,0.39024517,0.18234245,1.378443744033575,98.62155625596642,0.9987738620257005,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
5,0.81507105,0.90281284,0.215604,2.2459259256720543,97.75407407432795,0.9922409323044121,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
6,9.985682,3.160013,1.1768036,16.160333156585693,83.8396668434143,0.9115017428994179,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
7,0.5005272,0.7074795,0.2867403,4.528762027621269,95.47123797237873,0.9938267567194998,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
8,20.03354,4.4758844,0.7842723,10.54338589310646,89.45661410689354,0.7563498914241791,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
9,0.87598777,0.9359422,0.41263142,4.135379940271378,95.86462005972862,0.9905342319980264,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
10,9.252414,3.041778,0.9001145,8.613623678684235,91.38637632131577,0.9075676947832108,"{'num_layers': 1, 'hidden_size': 66, 'kernel_size': 12, 'dropout': 0.18461062540589218, 'learning_rate': 0.01, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'Adam', 'weight_decay': 3.339828230348852e-05}"
