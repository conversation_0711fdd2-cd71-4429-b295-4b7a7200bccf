import torch
import numpy as np
import pandas as pd
import json
import os
from datetime import datetime
import matplotlib.pyplot as plt

# 导入项目模块
from model import create_model
from data_preprocessing import DataPreprocessor

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ModelDeployer:
    """模型部署类"""
    
    def __init__(self, model_config_path: str):
        """
        初始化模型部署器
        
        Args:
            model_config_path (str): 模型配置文件路径
        """
        self.model_config_path = model_config_path
        self.model = None
        self.config = None
        self.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        self.load_model()
    
    def load_model(self):
        """加载模型和配置"""
        print("🔄 正在加载模型...")
        
        # 加载配置文件
        with open(self.model_config_path, 'r', encoding='utf-8') as f:
            model_info = json.load(f)
        
        self.config = model_info['config']
        model_path = model_info['model_path']
        
        print(f"📋 模型信息:")
        print(f"   - 模型类型: {model_info['model_type']}")
        print(f"   - 描述: {model_info['description']}")
        print(f"   - 时间戳: {model_info['timestamp']}")
        print(f"   - 源模型: {os.path.basename(model_info['source_model'])}")
        
        # 创建模型
        self.model = create_model(self.config).to(self.device)
        
        # 加载权重
        self.model.load_state_dict(torch.load(model_path, map_location=self.device))
        self.model.eval()
        
        print(f"✅ 模型加载成功！")
        print(f"   - 设备: {self.device}")
        print(f"   - 输入维度: {self.config['input_size']}")
        print(f"   - 隐藏层大小: {self.config['hidden_size']}")
        print(f"   - 网络层数: {self.config['num_layers']}")
    
    def predict(self, input_data: torch.Tensor) -> np.ndarray:
        """
        进行预测
        
        Args:
            input_data (torch.Tensor): 输入数据，形状为 [batch_size, sequence_length, input_size]
            
        Returns:
            np.ndarray: 预测结果
        """
        with torch.no_grad():
            input_data = input_data.to(self.device)
            predictions = self.model(input_data)
            return predictions.cpu().numpy()
    
    def predict_from_dataframe(self, df: pd.DataFrame, sequence_length: int = 24) -> np.ndarray:
        """
        从DataFrame进行预测
        
        Args:
            df (pd.DataFrame): 输入数据框
            sequence_length (int): 序列长度
            
        Returns:
            np.ndarray: 预测结果
        """
        # 数据预处理
        preprocessor = DataPreprocessor()
        
        # 应用与训练时相同的预处理步骤
        df_processed = preprocessor.prepare_features(df, sequence_length)
        
        # 准备序列数据
        X, _ = preprocessor.prepare_sequences(df_processed, sequence_length)
        
        # 进行预测
        predictions = self.predict(X)
        
        return predictions
    
    def get_model_info(self) -> dict:
        """获取模型信息"""
        return {
            'config': self.config,
            'device': self.device,
            'parameters': sum(p.numel() for p in self.model.parameters()),
            'trainable_parameters': sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        }

def demo_prediction():
    """演示预测功能"""
    print("=" * 60)
    print("电动汽车充电负荷预测模型部署演示")
    print("=" * 60)
    
    # 查找最新的模型配置文件
    output_dirs = [d for d in os.listdir('outputs') if os.path.isdir(os.path.join('outputs', d))]
    if not output_dirs:
        print("❌ 未找到输出目录")
        return
    
    latest_dir = max(output_dirs)
    config_path = os.path.join('outputs', latest_dir, 'final_model_config.json')
    
    if not os.path.exists(config_path):
        print(f"❌ 未找到模型配置文件: {config_path}")
        return
    
    print(f"📁 使用模型: {latest_dir}")
    
    # 初始化部署器
    deployer = ModelDeployer(config_path)
    
    # 显示模型信息
    model_info = deployer.get_model_info()
    print(f"\n📊 模型统计:")
    print(f"   - 总参数数量: {model_info['parameters']:,}")
    print(f"   - 可训练参数: {model_info['trainable_parameters']:,}")
    
    # 加载测试数据
    print(f"\n🔄 加载测试数据...")
    try:
        df = pd.read_csv('ev_charging_data.csv', encoding='utf-8-sig')
        print(f"✅ 数据加载成功，共 {len(df)} 行")
        
        # 重命名列（与训练时保持一致）
        column_mapping = {
            '充电时间': 'Timestamp',
            '降水量(mm)': 'Precipitation_mm',
            '平均气温(℃)': 'Average_Temperature_C',
            '最低气温(℃)': 'Min_Temperature_C',
            '最高气温(℃)': 'Max_Temperature_C',
            '总有功功率_总和(kW)': 'Charging_Load_kW'
        }
        df = df.rename(columns=column_mapping)
        
        # 转换时间列
        df['Timestamp'] = pd.to_datetime(df['Timestamp'], format='%Y/%m/%d %H:%M')
        
        # 取最后100个样本进行演示
        demo_df = df.tail(100).copy()
        print(f"📊 使用最后 {len(demo_df)} 个样本进行演示")
        
        # 创建模拟输入数据（简化版本）
        # 注意：实际使用时需要完整的特征工程流程
        sequence_length = 24
        input_size = model_info['config']['input_size']
        
        # 创建随机输入数据进行演示（实际应用中应使用真实的预处理数据）
        batch_size = 5
        demo_input = torch.randn(batch_size, sequence_length, input_size)
        
        print(f"\n🔮 进行预测演示...")
        print(f"   - 输入形状: {demo_input.shape}")
        
        # 进行预测
        predictions = deployer.predict(demo_input)
        
        print(f"✅ 预测完成！")
        print(f"   - 输出形状: {predictions.shape}")
        print(f"   - 预测结果样例: {predictions[:3].flatten()}")
        
        # 简单的可视化
        plt.figure(figsize=(10, 6))
        plt.plot(predictions.flatten(), 'b-', label='预测值', marker='o', markersize=4)
        plt.title('充电负荷预测结果演示')
        plt.xlabel('样本序号')
        plt.ylabel('预测充电负荷')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图表
        output_dir = os.path.join('outputs', latest_dir)
        plot_path = os.path.join(output_dir, 'prediction_demo.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 预测结果图表已保存: {plot_path}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def create_deployment_guide():
    """创建部署指南"""
    guide = """
# 模型部署指南

## 1. 模型文件说明
- `final_best_model.pth`: 最终的最佳模型权重文件
- `final_model_config.json`: 模型配置和元数据

## 2. 加载模型
```python
from deploy_model_example import ModelDeployer

# 初始化部署器
deployer = ModelDeployer('path/to/final_model_config.json')

# 进行预测
predictions = deployer.predict(input_tensor)
```

## 3. 输入数据格式
- 形状: [batch_size, sequence_length, input_size]
- 数据类型: torch.FloatTensor
- 设备: 自动适配 CPU/GPU

## 4. 预处理要求
输入数据需要经过与训练时相同的预处理步骤：
1. 数据清洗和填充
2. 特征工程（时间特征、气象特征、滞后特征等）
3. 特征缩放和选择
4. 序列化处理

## 5. 性能指标
基于十折交叉验证的最佳模型：
- 平均MAPE: ~5.90%
- 平均R2: ~0.9492
- 最佳折MAPE: 1.38%

## 6. 注意事项
- 确保输入数据的特征维度与训练时一致
- 模型已针对小时级充电负荷预测优化
- 建议定期重新训练以适应数据分布变化
"""
    
    with open('模型部署指南.md', 'w', encoding='utf-8') as f:
        f.write(guide)
    
    print("📖 部署指南已创建: 模型部署指南.md")

if __name__ == "__main__":
    # 运行演示
    demo_prediction()
    
    # 创建部署指南
    create_deployment_guide()
    
    print("\n🎉 模型部署演示完成！")
    print("\n📝 后续步骤:")
    print("   1. 根据实际需求调整预测接口")
    print("   2. 集成到生产系统中")
    print("   3. 设置监控和日志记录")
    print("   4. 定期评估模型性能")
