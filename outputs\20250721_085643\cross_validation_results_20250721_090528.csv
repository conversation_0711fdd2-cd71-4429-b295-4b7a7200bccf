﻿fold,mse,rmse,mae,mape,accuracy,r2,val_loss,model_path,params
1,4.2232976,2.0550663,1.1594348,9.658300131559372,90.34169986844063,0.9686267152428627,0.04276716150343418,outputs\20250721_085643\best_model_fold1.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
2,9.39789,3.0655978,0.8668776,10.156317800283432,89.84368219971657,0.9338212609291077,0.026203223193685215,outputs\20250721_085643\best_model_fold2.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
3,0.7237065,0.8507094,0.29973385,2.963794767856598,97.0362052321434,0.994766226503998,0.006396438577212393,outputs\20250721_085643\best_model_fold3.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
4,0.15316205,0.39135924,0.2739139,2.0827870815992355,97.91721291840076,0.9987668513786048,0.003464511343433211,outputs\20250721_085643\best_model_fold4.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
5,0.13241912,0.36389437,0.22090255,1.548078190535307,98.4519218094647,0.9987394362688065,0.005063675343990326,outputs\20250721_085643\best_model_fold5.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
6,20.580557,4.53658,0.7788458,12.227270007133484,87.77272999286652,0.8176044970750809,0.041121223398173846,outputs\20250721_085643\best_model_fold6.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
7,0.5544064,0.74458474,0.36898267,5.030600726604462,94.96939927339554,0.9931622389703989,0.011064058014502129,outputs\20250721_085643\best_model_fold7.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
8,13.334755,3.6516783,0.7865459,10.198774188756943,89.80122581124306,0.8378212600946426,0.035041437561934195,outputs\20250721_085643\best_model_fold8.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
9,3.4719033,1.8633044,0.8847386,8.299322426319122,91.70067757368088,0.9624832309782505,0.0194717423679928,outputs\20250721_085643\best_model_fold9.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
10,0.84480405,0.91913223,0.63696593,4.277041554450989,95.72295844554901,0.9915603436529636,0.02194151406486829,outputs\20250721_085643\best_model_fold10.pth,"{'num_layers': 1, 'hidden_size': 152, 'kernel_size': 12, 'dropout': 0.555701407221051, 'learning_rate': 0.005845372360853762, 'loss_type': 'SmoothL1Loss', 'optimizer_type': 'AdamW', 'weight_decay': 0.0001}"
