#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速训练测试脚本

验证改进后的系统能够正常进行模型训练和评估
"""

import torch
import numpy as np
import logging
from train import EVChargingPredictor
from model import create_model

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def quick_train_test():
    """快速训练测试"""
    print("=" * 60)
    print("快速训练测试")
    print("=" * 60)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 检查设备
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"使用设备: {device}")
    
    # 初始化预测器
    predictor = EVChargingPredictor(device=device)
    
    # 准备数据
    print("准备数据...")
    train_loader, val_loader, test_loader, target_scaler = predictor.prepare_data('ev_charging_data.csv')
    
    print(f"数据准备完成:")
    print(f"- 训练集批次: {len(train_loader)}")
    print(f"- 验证集批次: {len(val_loader)}")
    print(f"- 测试集批次: {len(test_loader)}")
    print(f"- 输入特征维度: {predictor.input_size}")
    
    # 创建简化的模型配置（用于快速测试）
    config = {
        'input_size': predictor.input_size,
        'hidden_size': 32,  # 减小隐藏层大小以加快训练
        'num_layers': 1,    # 减少层数
        'output_size': 1,
        'kernel_size': 3,
        'dropout': 0.2,
        'learning_rate': 0.01,  # 增大学习率以加快收敛
        'loss_type': 'MSELoss',
        'optimizer_type': 'Adam',
        'weight_decay': 1e-4
    }
    
    print("创建模型...")
    model = create_model(config).to(device)
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 快速训练几个epoch
    print("开始快速训练...")
    import torch.nn as nn
    import torch.optim as optim
    from tqdm import tqdm
    
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])
    
    num_epochs = 3  # 只训练3个epoch用于测试
    
    for epoch in range(num_epochs):
        model.train()
        train_loss = 0.0
        
        # 训练
        train_pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{num_epochs} [训练]', leave=False)
        for X, y in train_pbar:
            X, y = X.to(device), y.to(device)
            
            optimizer.zero_grad()
            output = model(X)
            loss = criterion(output, y)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_pbar.set_postfix(loss=f'{loss.item():.4f}')
        
        avg_train_loss = train_loss / len(train_loader)
        
        # 验证
        model.eval()
        val_loss = 0.0
        with torch.no_grad():
            for X, y in val_loader:
                X, y = X.to(device), y.to(device)
                output = model(X)
                loss = criterion(output, y)
                val_loss += loss.item()
        
        avg_val_loss = val_loss / len(val_loader)
        
        print(f'Epoch {epoch+1}/{num_epochs} - 训练损失: {avg_train_loss:.4f}, 验证损失: {avg_val_loss:.4f}')
    
    # 测试评估
    print("测试模型评估...")
    model.eval()
    predictions = []
    actuals = []
    
    with torch.no_grad():
        for X, y in test_loader:
            X, y = X.to(device), y.to(device)
            output = model(X)
            predictions.extend(output.cpu().numpy())
            actuals.extend(y.cpu().numpy())
    
    # 反归一化
    predictions = np.array(predictions).reshape(-1, 1)
    actuals = np.array(actuals).reshape(-1, 1)
    predictions_orig = target_scaler.inverse_transform(predictions)
    actuals_orig = target_scaler.inverse_transform(actuals)
    
    # 计算评估指标
    mse = np.mean((predictions_orig - actuals_orig) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(predictions_orig - actuals_orig))
    
    # 计算MAPE（避免除零）
    mask = np.abs(actuals_orig) > 1e-6
    if np.any(mask):
        mape = np.mean(np.abs((actuals_orig[mask] - predictions_orig[mask]) / actuals_orig[mask])) * 100
    else:
        mape = 0.0
    
    print(f"\n测试集评估结果:")
    print(f"- MSE: {mse:.4f}")
    print(f"- RMSE: {rmse:.4f}")
    print(f"- MAE: {mae:.4f}")
    print(f"- MAPE: {mape:.2f}%")
    
    # 显示一些预测样例
    print(f"\n预测样例 (前10个):")
    print("实际值 -> 预测值")
    for i in range(min(10, len(actuals_orig))):
        actual = actuals_orig[i, 0]
        pred = predictions_orig[i, 0]
        print(f"{actual:8.4f} -> {pred:8.4f} (误差: {abs(actual-pred):6.4f})")
    
    return True

def main():
    """主函数"""
    try:
        success = quick_train_test()
        if success:
            print("\n" + "=" * 60)
            print("✅ 快速训练测试成功完成！")
            print("✅ 改进后的系统能够正常训练和评估")
            print("=" * 60)
        return success
    except Exception as e:
        print(f"\n❌ 快速训练测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 系统完全正常工作！可以开始完整训练。")
    else:
        print("\n❌ 系统存在问题，请检查错误信息。")
